# Python 函数基础与应用

## 函数概述与优势
### 函数是带名字的代码块，用于完成具体的工作，可以反复调用以复用逻辑。
### 使用函数让编写、阅读、测试和修复程序更容易。
### 可通过不同方式向函数传递信息，包括位置实参、关键字实参、默认值等。
### 函数可处理数据并返回结果，提升主程序的简洁性。
### 函数可以存储在模块中，使主程序文件更整洁、复用性更高。
### 结构化编写和合理命名函数有助于代码的理解和维护。

## 函数定义与调用规则
### 使用 def 关键字定义函数，并指定函数名与形参。
### 函数体由缩进代码组成，通常包含文档字符串描述功能。
### 函数调用时需使用函数名和括号，并传入必要实参。
### 形参是函数定义时用来接收信息的变量，实参则是在调用时传递的值。
### 函数可以多次调用，提升代码复用和效率。
### 函数调用中的实参顺序或名称需和形参正确匹配。

## 实参传递方式
### 位置实参要求实参顺序与形参一致，顺序错误会导致意外结果。
### 关键字实参通过名称明确指定形参，解决顺序混淆问题。
### 可为形参指定默认值，简化函数调用并明确典型用法。
### 默认值形参需置于无默认值形参之后，以确保正确关联。
### 位置实参、关键字实参和默认值可以混合使用，灵活调用函数。
### 实参数量必须与函数定义匹配，过多或过少都引发错误。

## 函数返回值与复杂数据处理
### 使用 return 语句将结果从函数返回到调用处。
### 返回值可赋给变量，用于后续处理或显示。
### 可通过默认值和条件语句让某些实参变为可选项。
### 函数能返回字典、列表等复杂数据结构，方便信息组织和扩展。
### 函数与循环、条件语句等结构结合，可实现更强大的交互和逻辑。
### 在循环中调用函数需设计合理的退出条件，确保程序可控。

## 列表传递与数据保护
### 向函数传递列表后，函数可遍历并处理每个元素。
### 函数可直接修改传入的列表，影响原始数据。
### 可通过传递列表副本避免原始列表被修改，保护数据完整性。
### 使用函数组织代码，使主程序结构更清晰、易扩展和维护。
### 每个函数应只负责一项具体任务，有助于代码分解和复用。

## 可变参数的使用
### 使用 *args 语法收集任意数量的位置实参，适合不定参数情况。
### *args 收集的内容为元组，可遍历处理所有传入值。
### 可结合位置实参和 *args，需将 *args 放在参数列表最后。
### 使用 **kwargs 语法收集任意数量的关键字实参，形成字典。
### **kwargs 适合接收不确定数量和名称的参数，实现高度灵活函数。
### 混合使用各种参数类型，提高函数的通用性和适应性。

## 模块与代码复用
### 模块是包含函数的 .py 文件，可通过 import 语句导入使用。
### 导入整个模块后，可通过“模块名.函数名”调用其中函数。
### 可只导入特定函数，调用时无需模块名前缀。
### 可用 as 给函数或模块指定别名，避免名称冲突或简化调用。
### 使用星号 * 可导入模块中所有函数，但不建议在大型项目中使用。
### 推荐只导入需要的函数或通过模块名调用，保持代码清晰易读。

## 编写规范与代码质量
### 函数名应描述性强，只用小写字母和下划线，模块命名亦如此。
### 每个函数需包含文档字符串，简要说明功能与使用方法。
### 给形参指定默认值时，等号两侧无空格，函数调用遵循同样约定。
### 单行长度不超过 79 字符，参数多时可换行并缩进对齐。
### 相邻函数之间用两个空行分隔，便于代码结构辨识。
### 所有 import 语句应在文件开头，除非有注释说明。

## 函数优势总结
### 函数让代码块复用，只需一行调用即可执行复杂任务。
### 修改函数只需改一个地方，所有调用均受益于更新。
### 良好函数名和结构让程序逻辑更清晰，方便理解与维护。
### 函数有助于测试和调试，可分别验证每个独立功能。
### 结构化函数编写让团队协作和代码共享更高效。
### 掌握函数为后续学习类和更复杂编程概念奠定基础。