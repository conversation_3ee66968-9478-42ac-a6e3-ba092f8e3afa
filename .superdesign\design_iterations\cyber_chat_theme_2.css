:root {
  --background: oklch(0.12 0.01 150);
  --foreground: oklch(0.92 0.05 150);
  --card: oklch(0.15 0.02 150);
  --card-foreground: oklch(0.95 0.05 150);
  --popover: oklch(0.1 0.01 150);
  --popover-foreground: oklch(0.95 0.05 150);
  --primary: oklch(0.85 0.28 145);
  --primary-foreground: oklch(0.1 0.01 150);
  --secondary: oklch(0.6 0.15 145);
  --secondary-foreground: oklch(0.1 0.01 150);
  --muted: oklch(0.25 0.02 150);
  --muted-foreground: oklch(0.7 0.05 150);
  --accent: oklch(0.75 0.25 145);
  --accent-foreground: oklch(0.1 0.01 150);
  --destructive: oklch(0.7 0.25 20);
  --destructive-foreground: oklch(0.1 0.01 150);
  --border: oklch(0.85 0.28 145 / 0.4);
  --input: oklch(0.15 0.02 150);
  --ring: oklch(0.85 0.28 145 / 0.5);
  --chart-1: oklch(0.85 0.28 145);
  --chart-2: oklch(0.75 0.25 145);
  --chart-3: oklch(0.65 0.20 145);
  --chart-4: oklch(0.55 0.18 145);
  --chart-5: oklch(0.45 0.15 145);
  --sidebar: oklch(0.14 0.02 150);
  --sidebar-foreground: oklch(0.9 0.05 150);
  --sidebar-primary: oklch(0.85 0.28 145);
  --sidebar-primary-foreground: oklch(0.1 0.01 150);
  --sidebar-accent: oklch(0.75 0.25 145);
  --sidebar-accent-foreground: oklch(0.1 0.01 150);
  --sidebar-border: oklch(0.85 0.28 145 / 0.2);
  --sidebar-ring: oklch(0.85 0.28 145 / 0.5);
  --font-sans: 'Geist Mono', 'Space Mono', 'Noto Sans SC', sans-serif;
  --font-serif: 'Geist Mono', 'Noto Serif SC', serif;
  --font-mono: 'Geist Mono', 'Space Mono', 'Fira Code', 'Noto Sans SC', monospace;
  --radius: 0.125rem;
  --spacing: 0.25rem;
  --shadow-2xs: 0 0 2px 0px oklch(0.85 0.28 145 / 0.2);
  --shadow-xs: 0 0 4px 0px oklch(0.85 0.28 145 / 0.2);
  --shadow-sm: 0 0 6px 1px oklch(0.85 0.28 145 / 0.25);
  --shadow: 0 0 8px 2px oklch(0.85 0.28 145 / 0.3);
  --shadow-md: 0 0 12px 3px oklch(0.85 0.28 145 / 0.3);
  --shadow-lg: 0 0 16px 4px oklch(0.85 0.28 145 / 0.35);
  --shadow-xl: 0 0 24px 6px oklch(0.85 0.28 145 / 0.35);
  --shadow-2xl: 0 0 32px 8px oklch(0.85 0.28 145 / 0.4);
}