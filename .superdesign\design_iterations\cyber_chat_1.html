<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyber Minimalist Chat</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="cyber_chat_theme_1.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Geist+Mono:wght@400;700&family=Noto+Sans+SC:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
    <style>
        :root {
            color-scheme: dark;
        }
        body {
            background-color: var(--background);
            color: var(--foreground);
            font-family: var(--font-sans);
        }
        .glow-border {
            box-shadow: var(--shadow-sm);
            transition: box-shadow 300ms ease-out;
        }
        .glow-border:focus-within {
            box-shadow: var(--shadow-md);
            outline: none;
        }
        .btn-glow:hover {
            box-shadow: var(--shadow-lg);
            transform: scale(1.05);
        }
        .btn-glow:active {
            transform: scale(0.95);
            box-shadow: var(--shadow-sm);
        }
        .ai-msg, .user-msg {
            opacity: 0;
            transform: translateY(10px);
            animation: message-entrance 700ms ease-out forwards;
        }
        .user-msg {
            animation-name: user-message-entrance;
            animation-duration: 300ms;
        }
        @keyframes message-entrance {
            from { opacity: 0; transform: translateY(10px) blur(3px); }
            to { opacity: 1; transform: translateY(0) blur(0); }
        }
        @keyframes user-message-entrance {
            from { opacity: 0.8; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
        }
        .typing-indicator span {
            animation: typing-glow 1.5s infinite;
        }
        .typing-indicator span:nth-child(2) { animation-delay: 0.25s; }
        .typing-indicator span:nth-child(3) { animation-delay: 0.5s; }
        @keyframes typing-glow {
            0%, 100% { box-shadow: none; }
            50% { box-shadow: var(--shadow-md); }
        }
        .sidebar {
            transform: translateX(-100%);
            transition: transform 400ms ease-in-out;
        }
        .sidebar-open .sidebar {
            transform: translateX(0);
        }
        .sidebar-open .main-content {
            filter: blur(5px) brightness(0.7);
            transition: filter 400ms ease-in-out;
        }
    </style>
</head>
<body class="flex h-screen antialiased">

    <!-- Sidebar (Desktop) -->
    <aside class="hidden md:flex md:flex-col w-64 bg-[var(--sidebar)] border-r border-[var(--sidebar-border)] p-4 transition-transform duration-300 ease-in-out">
        <h2 class="text-lg font-bold text-[var(--sidebar-foreground)] mb-4">Chat History</h2>
        <nav class="flex-1 space-y-2">
            <a href="#" class="flex items-center p-2 rounded-md bg-[var(--sidebar-primary)] text-[var(--sidebar-primary-foreground)]">
                <i data-lucide="message-square" class="w-4 h-4 mr-2"></i>
                <span>Session 1</span>
            </a>
            <a href="#" class="flex items-center p-2 rounded-md hover:bg-[var(--sidebar-accent)]">
                <i data-lucide="message-square" class="w-4 h-4 mr-2"></i>
                <span>Session 2</span>
            </a>
        </nav>
        <button class="w-full mt-4 p-2 rounded-md border border-[var(--sidebar-border)] hover:bg-[var(--sidebar-accent)] transition-colors">
            + New Chat
        </button>
    </aside>

    <!-- Main Chat Area -->
    <div class="flex-1 flex flex-col h-full">
        <!-- Header -->
        <header class="flex items-center justify-between p-4 border-b border-[var(--border)]">
            <button class="md:hidden" onclick="document.body.classList.toggle('sidebar-open')">
                <i data-lucide="menu" class="w-6 h-6"></i>
            </button>
            <h1 class="text-xl font-bold text-center text-[var(--primary)] tracking-widest">&lt;[ CYBER CHAT ]&gt;</h1>
            <div class="w-6"></div>
        </header>

        <!-- Chat Messages -->
        <main id="chat-area" class="flex-1 p-4 overflow-y-auto space-y-6">
            <!-- AI Message -->
            <div class="flex items-start gap-3 ai-msg" style="animation-delay: 0.5s;">
                <div class="w-8 h-8 flex-shrink-0 rounded-full bg-[var(--accent)] flex items-center justify-center text-[var(--accent-foreground)] font-bold">A</div>
                <div class="p-3 rounded-lg bg-[var(--card)] max-w-md">
                    <p>Hello, human. I am a design agent. How can I assist you today?</p>
                </div>
            </div>

            <!-- User Message -->
            <div class="flex items-start gap-3 justify-end user-msg" style="animation-delay: 1s;">
                <div class="p-3 rounded-lg bg-[var(--primary)] text-[var(--primary-foreground)] max-w-md">
                    <p>你好！请问你能做什么？</p>
                </div>
                 <div class="w-8 h-8 flex-shrink-0 rounded-full bg-gray-700 flex items-center justify-center text-gray-300 font-bold">Y</div>
            </div>
            
            <!-- AI Typing Indicator -->
            <div class="flex items-start gap-3 ai-msg" style="animation-delay: 1.5s;">
                <div class="w-8 h-8 flex-shrink-0 rounded-full bg-[var(--accent)] flex items-center justify-center text-[var(--accent-foreground)] font-bold">A</div>
                <div class="p-3 rounded-lg bg-[var(--card)] flex items-center space-x-2">
                    <span class="typing-indicator w-2 h-2 rounded-full bg-[var(--primary)]"></span>
                    <span class="typing-indicator w-2 h-2 rounded-full bg-[var(--primary)]"></span>
                    <span class="typing-indicator w-2 h-2 rounded-full bg-[var(--primary)]"></span>
                </div>
            </div>

        </main>

        <!-- Input Footer -->
        <footer class="p-4 border-t border-[var(--border)]">
            <div class="relative flex items-center glow-border rounded-lg">
                <input type="text" placeholder="> Type your message..." class="w-full bg-transparent pl-4 pr-12 py-3 text-[var(--foreground)] focus:outline-none">
                <button class="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-[var(--primary)] text-[var(--primary-foreground)] btn-glow transition-all duration-200">
                    <i data-lucide="send-horizontal" class="w-5 h-5"></i>
                </button>
            </div>
        </footer>
    </div>

    <script>
        lucide.createIcons();

        // Auto-scroll to bottom
        const chatArea = document.getElementById('chat-area');
        chatArea.scrollTop = chatArea.scrollHeight;
    </script>
</body>
</html>
