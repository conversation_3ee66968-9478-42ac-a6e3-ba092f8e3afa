:root {
  --background: oklch(0.15 0.02 240);
  --foreground: oklch(0.9 0.01 240);
  --card: oklch(0.2 0.02 240);
  --card-foreground: oklch(0.95 0.01 240);
  --popover: oklch(0.1 0.01 240);
  --popover-foreground: oklch(0.95 0.01 240);
  --primary: oklch(0.8 0.25 180);
  --primary-foreground: oklch(0.1 0.01 240);
  --secondary: oklch(0.7 0.28 290);
  --secondary-foreground: oklch(0.1 0.01 240);
  --muted: oklch(0.3 0.02 240);
  --muted-foreground: oklch(0.7 0.01 240);
  --accent: oklch(0.85 0.2 150);
  --accent-foreground: oklch(0.1 0.01 240);
  --destructive: oklch(0.7 0.25 20);
  --destructive-foreground: oklch(0.1 0.01 240);
  --border: oklch(0.8 0.25 180 / 0.4);
  --input: oklch(0.2 0.02 240);
  --ring: oklch(0.8 0.25 180 / 0.5);
  --chart-1: oklch(0.8 0.25 180);
  --chart-2: oklch(0.7 0.28 290);
  --chart-3: oklch(0.85 0.2 150);
  --chart-4: oklch(0.8 0.25 50);
  --chart-5: oklch(0.7 0.25 20);
  --sidebar: oklch(0.12 0.02 240);
  --sidebar-foreground: oklch(0.85 0.01 240);
  --sidebar-primary: oklch(0.8 0.25 180);
  --sidebar-primary-foreground: oklch(0.1 0.01 240);
  --sidebar-accent: oklch(0.85 0.2 150);
  --sidebar-accent-foreground: oklch(0.1 0.01 240);
  --sidebar-border: oklch(0.8 0.25 180 / 0.2);
  --sidebar-ring: oklch(0.8 0.25 180 / 0.5);
  --font-sans: 'Geist Mono', 'Space Mono', 'Noto Sans SC', sans-serif;
  --font-serif: 'Geist Mono', 'Noto Serif SC', serif;
  --font-mono: 'Geist Mono', 'Space Mono', 'Fira Code', 'Noto Sans SC', monospace;
  --radius: 0.25rem;
  --spacing: 0.25rem;
  --shadow-2xs: 0 0 2px 0px oklch(0.8 0.25 180 / 0.2);
  --shadow-xs: 0 0 4px 0px oklch(0.8 0.25 180 / 0.2);
  --shadow-sm: 0 0 6px 1px oklch(0.8 0.25 180 / 0.25);
  --shadow: 0 0 8px 2px oklch(0.8 0.25 180 / 0.3);
  --shadow-md: 0 0 12px 3px oklch(0.8 0.25 180 / 0.3);
  --shadow-lg: 0 0 16px 4px oklch(0.8 0.25 180 / 0.35);
  --shadow-xl: 0 0 24px 6px oklch(0.8 0.25 180 / 0.35);
  --shadow-2xl: 0 0 32px 8px oklch(0.8 0.25 180 / 0.4);
}